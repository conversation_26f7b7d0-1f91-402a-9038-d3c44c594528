import { useMutation } from '@tanstack/react-query';
import { type SendMessagePayload, MUTATION_KEYS } from '../../types/api';
import chatService from '../../services/chatService';
import { useChatStore } from '../../store';

// Simplified hook for sending messages
export const useSendMessage = () => {
  const { addMessage, setTyping, setThreadId, currentThreadId } = useChatStore();

  return useMutation({
    mutationKey: [MUTATION_KEYS.SEND_MESSAGE],
    mutationFn: async (payload: SendMessagePayload) => {
      // Add user message immediately
      addMessage(payload.message, 'user');
      // Show typing indicator
      setTyping(true);

      // Include current threadId in the payload if available
      const requestPayload = {
        ...payload,
        ...(currentThreadId && { threadId: currentThreadId })
      };
      // Call API
      const response = await chatService.sendMessage(requestPayload);
      return response;
    },
    onSuccess: (data) => {
      // Hide typing indicator
      setTyping(false);
      // Extract message and threadId from the API response
      let responseText = '';
      let threadId = null;

      if (data && typeof data === 'object') {
        const apiData = data as any;

        // Extract the message field (this is the primary response text)
        responseText = apiData.message || apiData.response || apiData.text || apiData.content;
        // Extract the threadId for conversation continuity
        threadId = apiData.threadId;
        // If no message found, fallback to JSON string
        if (!responseText) {
          responseText = JSON.stringify(data);
        }
      } else if (typeof data === 'string') {
        responseText = data;
      } else {
        responseText = 'Received response from AI';
      }

      // Update threadId in store if received
      if (threadId) {
        setThreadId(threadId);
      }

      addMessage(responseText, 'agent');
    },
    onError: (error) => {
      // Hide typing indicator
      setTyping(false);
      // Add error message
      console.error('API Error:', error);
      addMessage('Sorry, I encountered an error. Please try again.', 'agent');
    },
  });
};

// Simplified utility hook for chat operations
export const useChatOperations = () => {
  const sendMessage = useSendMessage();

  return {
    sendMessage: sendMessage.mutate,
    sendMessageAsync: sendMessage.mutateAsync,
    isSendingMessage: sendMessage.isPending,
  };
};
