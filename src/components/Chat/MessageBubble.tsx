import React from 'react';
import { formatDistanceToNow } from 'date-fns';
import clsx from 'clsx';

// Simplified message interface
interface SimpleMessage {
  id: string;
  content: string;
  sender: 'user' | 'agent';
  timestamp: Date;
}

interface MessageBubbleProps {
  message: SimpleMessage;
  isOwn: boolean;
  showTimestamp?: boolean;
  className?: string;
  children?: React.ReactNode;
}

const MessageBubble: React.FC<MessageBubbleProps> = ({
  message,
  isOwn,
  showTimestamp = true,
  className,
  children,
}) => {
  const formatTimestamp = (timestamp: Date) => {
    try {
      return formatDistanceToNow(timestamp, { addSuffix: true });
    } catch {
      return timestamp.toLocaleTimeString();
    }
  };

  return (
    <div
      className={clsx(
        'message-bubble',
        {
          'message-bubble--own': isOwn,
          'message-bubble--other': !isOwn,
        },
        className
      )}
    >
      <div className="message-bubble__content">
        <div className="message-bubble__text">
          {message.content}
        </div>

        {showTimestamp && (
          <div className="message-bubble__timestamp">
            {formatTimestamp(message.timestamp)}
          </div>
        )}
      </div>

      {children}
    </div>
  );
};

export default MessageBubble;
