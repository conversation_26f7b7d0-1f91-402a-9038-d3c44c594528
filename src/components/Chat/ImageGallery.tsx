import React, { useState } from 'react';
import { Expand } from 'lucide-react';
import clsx from 'clsx';

interface ImageGalleryProps {
  images: string[];
  className?: string;
  onImageClick?: (imageIndex: number) => void;
}

const ImageGallery: React.FC<ImageGalleryProps> = ({
  images,
  className,
  onImageClick,
}) => {
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);

  if (!images || images.length === 0) {
    return null;
  }

  const handleImageClick = (index: number) => {
    onImageClick?.(index);
  };

  const handleImageError = (event: React.SyntheticEvent<HTMLImageElement>) => {
    // Hide the image on error
    event.currentTarget.style.display = 'none';
  };

  const getGridClass = (count: number) => {
    if (count === 1) return 'image-gallery__grid--single';
    if (count === 2) return 'image-gallery__grid--two';
    if (count === 3) return 'image-gallery__grid--three';
    return 'image-gallery__grid--multiple';
  };

  return (
    <div className={clsx('image-gallery', className)}>
      <div className={clsx('image-gallery__grid', getGridClass(images.length))}>
        {images.map((imageData, index) => (
          <div
            key={index}
            className="image-gallery__item"
            onMouseEnter={() => setHoveredIndex(index)}
            onMouseLeave={() => setHoveredIndex(null)}
            onClick={() => handleImageClick(index)}
          >
            <img
              src={`data:image/jpeg;base64,${imageData}`}
              alt={`Image ${index + 1}`}
              className="image-gallery__image"
              onError={handleImageError}
              loading="lazy"
            />
            
            {/* Expand icon overlay on hover */}
            {hoveredIndex === index && (
              <div className="image-gallery__overlay">
                <div className="image-gallery__expand-icon">
                  <Expand size={20} />
                </div>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default ImageGallery;
