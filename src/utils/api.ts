import type { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';
import axios from 'axios';
import {
  type ApiResponse,
  type ApiError,
  type HttpClientConfig,
  type RetryConfig,
  DEFAULT_API_CONFIG,
  API_ERROR_CODES,
  type ApiErrorCode,
} from '../types/api';

// Create axios instance with default configuration
const createApiClient = (config: Partial<HttpClientConfig> = {}): AxiosInstance => {
  const finalConfig = { ...DEFAULT_API_CONFIG, ...config };
  
  const client = axios.create({
    baseURL: finalConfig.baseURL,
    timeout: finalConfig.timeout,
    headers: finalConfig.headers,
  });
  
  // Request interceptor
  client.interceptors.request.use(
    (config) => {
      // Add timestamp to requests (store in headers for tracking)
      config.headers['X-Request-Start-Time'] = Date.now().toString();

      // Add request ID for tracking
      config.headers['X-Request-ID'] = generateRequestId();

      // Add authentication headers if available
      if (import.meta.env.VITE_API_KEY) {
        config.headers['X-API-Key'] = import.meta.env.VITE_API_KEY;
      }

      if (import.meta.env.VITE_AUTH_TOKEN) {
        config.headers['Authorization'] = `Bearer ${import.meta.env.VITE_AUTH_TOKEN}`;
      }

      // Log request in development
      if (import.meta.env.DEV) {
        // eslint-disable-next-line no-console
        console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`);
      }

      return config;
    },
    (error) => {
      return Promise.reject(error);
    }
  );
  
  // Response interceptor
  client.interceptors.response.use(
    (response: AxiosResponse) => {
      // Calculate request duration
      const startTime = parseInt(response.config.headers['X-Request-Start-Time'] as string || '0');
      const duration = Date.now() - startTime;
      
      // Log response in development
      if (import.meta.env.DEV) {
        // eslint-disable-next-line no-console
        console.log(`✅ API Response: ${response.status} ${response.config.url} (${duration}ms)`);
      }
      
      // Transform response to our standard format
      const apiResponse: ApiResponse = {
        data: response.data,
        success: true,
        message: response.statusText,
        timestamp: new Date().toISOString(),
      };
      
      return { ...response, data: apiResponse };
    },
    (error: AxiosError) => {
      // Calculate request duration
      const startTime = parseInt(error.config?.headers?.['X-Request-Start-Time'] as string || '0');
      const duration = Date.now() - startTime;
      
      // Log error in development
      if (import.meta.env.DEV) {
        // eslint-disable-next-line no-console
        console.error(`❌ API Error: ${error.response?.status || 'Network'} ${error.config?.url} (${duration}ms)`);
      }
      
      // Transform error to our standard format
      const apiError = transformAxiosError(error);
      return Promise.reject(apiError);
    }
  );
  
  return client;
};

// Transform Axios error to our ApiError format
const transformAxiosError = (error: AxiosError): ApiError => {
  let code: ApiErrorCode;
  let message: string;
  let details: unknown = {};
  
  if (error.code === 'ECONNABORTED' || error.code === 'ETIMEDOUT') {
    code = API_ERROR_CODES.TIMEOUT;
    message = 'Request timeout';
  } else if (error.code === 'ERR_NETWORK') {
    code = API_ERROR_CODES.NETWORK_ERROR;
    message = 'Network error';
  } else if (error.response) {
    // Server responded with error status
    const status = error.response.status;
    
    switch (status) {
      case 401:
        code = API_ERROR_CODES.UNAUTHORIZED;
        message = 'Unauthorized';
        break;
      case 403:
        code = API_ERROR_CODES.FORBIDDEN;
        message = 'Forbidden';
        break;
      case 404:
        code = API_ERROR_CODES.NOT_FOUND;
        message = 'Not found';
        break;
      case 422:
        code = API_ERROR_CODES.VALIDATION_ERROR;
        message = 'Validation error';
        details = error.response.data;
        break;
      case 429:
        code = API_ERROR_CODES.RATE_LIMITED;
        message = 'Rate limited';
        break;
      case 503:
        code = API_ERROR_CODES.SERVICE_UNAVAILABLE;
        message = 'Service unavailable';
        break;
      default:
        code = API_ERROR_CODES.SERVER_ERROR;
        message = error.response.data?.message || 'Server error';
        details = error.response.data;
    }
  } else {
    code = API_ERROR_CODES.NETWORK_ERROR;
    message = error.message || 'Unknown error';
  }
  
  return {
    code,
    message,
    details,
    timestamp: new Date().toISOString(),
  };
};

// Generate unique request ID
const generateRequestId = (): string => {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

// Retry logic
const createRetryConfig = (config: Partial<RetryConfig> = {}): RetryConfig => {
  return {
    retries: 3,
    retryDelay: 1000,
    retryCondition: (error: ApiError) => {
      // Retry on network errors, timeouts, and 5xx errors
      return [
        API_ERROR_CODES.NETWORK_ERROR,
        API_ERROR_CODES.TIMEOUT,
        API_ERROR_CODES.SERVER_ERROR,
        API_ERROR_CODES.SERVICE_UNAVAILABLE,
      ].includes(error.code);
    },
    ...config,
  };
};

// Retry wrapper function
const withRetry = async <T>(
  operation: () => Promise<T>,
  retryConfig: RetryConfig
): Promise<T> => {
  let lastError: ApiError;
  
  for (let attempt = 0; attempt <= retryConfig.retries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error as ApiError;
      
      // Don't retry if it's the last attempt or if retry condition is not met
      if (attempt === retryConfig.retries || !retryConfig.retryCondition?.(lastError)) {
        throw lastError;
      }
      
      // Call onRetry callback if provided
      retryConfig.onRetry?.(attempt + 1, lastError);
      
      // Wait before retrying with exponential backoff
      const delay = retryConfig.retryDelay * Math.pow(2, attempt);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  throw lastError || new Error('Unknown error occurred during retry');
};

// Create the main API client
export const apiClient = createApiClient();

// API client with retry
export const apiClientWithRetry = {
  async get<T>(url: string, config?: AxiosRequestConfig, retryConfig?: Partial<RetryConfig>): Promise<ApiResponse<T>> {
    const retry = createRetryConfig(retryConfig);
    return withRetry(() => apiClient.get<T>(url, config).then(res => res.data), retry);
  },
  
  async post<T>(url: string, data?: unknown, config?: AxiosRequestConfig, retryConfig?: Partial<RetryConfig>): Promise<ApiResponse<T>> {
    const retry = createRetryConfig(retryConfig);
    return withRetry(() => apiClient.post<T>(url, data, config).then(res => res.data), retry);
  },
  
  async put<T>(url: string, data?: unknown, config?: AxiosRequestConfig, retryConfig?: Partial<RetryConfig>): Promise<ApiResponse<T>> {
    const retry = createRetryConfig(retryConfig);
    return withRetry(() => apiClient.put<T>(url, data, config).then(res => res.data), retry);
  },
  
  async delete<T>(url: string, config?: AxiosRequestConfig, retryConfig?: Partial<RetryConfig>): Promise<ApiResponse<T>> {
    const retry = createRetryConfig(retryConfig);
    return withRetry(() => apiClient.delete<T>(url, config).then(res => res.data), retry);
  },
  
  async patch<T>(url: string, data?: unknown, config?: AxiosRequestConfig, retryConfig?: Partial<RetryConfig>): Promise<ApiResponse<T>> {
    const retry = createRetryConfig(retryConfig);
    return withRetry(() => apiClient.patch<T>(url, data, config).then(res => res.data), retry);
  },
};

// Health check utility
export const healthCheck = async (): Promise<boolean> => {
  try {
    await apiClient.get('/health');
    return true;
  } catch {
    return false;
  }
};

// Request cancellation utilities
export const createCancelToken = () => {
  return axios.CancelToken.source();
};

export const isRequestCancelled = (error: unknown): boolean => {
  return axios.isCancel(error);
};

// Request timeout utilities
export const withTimeout = <T>(promise: Promise<T>, timeoutMs: number): Promise<T> => {
  return Promise.race([
    promise,
    new Promise<never>((_, reject) => {
      setTimeout(() => {
        reject(new Error(`Request timeout after ${timeoutMs}ms`));
      }, timeoutMs);
    }),
  ]);
};

// Request queue for managing concurrent requests
class RequestQueue {
  private queue: Array<() => Promise<unknown>> = [];
  private running = 0;
  private maxConcurrent = 5;
  
  async add<T>(request: () => Promise<T>): Promise<T> {
    return new Promise((resolve, reject) => {
      this.queue.push(async () => {
        try {
          this.running++;
          const result = await request();
          resolve(result);
        } catch (error) {
          reject(error);
        } finally {
          this.running--;
          this.processQueue();
        }
      });
      
      this.processQueue();
    });
  }
  
  private processQueue() {
    if (this.running >= this.maxConcurrent || this.queue.length === 0) {
      return;
    }
    
    const request = this.queue.shift();
    if (request) {
      request();
    }
  }
  
  setMaxConcurrent(max: number) {
    this.maxConcurrent = max;
  }
  
  clear() {
    this.queue = [];
  }
  
  getQueueLength() {
    return this.queue.length;
  }
  
  getRunningCount() {
    return this.running;
  }
}

export const requestQueue = new RequestQueue();
