// Image Gallery Component Styles

.image-gallery {
  margin-top: var(--spacing-sm);
  
  &__grid {
    display: grid;
    gap: var(--spacing-xs);
    border-radius: var(--border-radius-md);
    overflow: hidden;
    
    &--single {
      grid-template-columns: 1fr;
      max-width: 300px;
    }
    
    &--two {
      grid-template-columns: 1fr 1fr;
      max-width: 400px;
    }
    
    &--three {
      grid-template-columns: 1fr 1fr;
      grid-template-rows: 1fr 1fr;
      max-width: 400px;
      
      .image-gallery__item:first-child {
        grid-column: 1 / -1;
      }
    }
    
    &--multiple {
      grid-template-columns: 1fr 1fr;
      max-width: 400px;
      
      .image-gallery__item:nth-child(n+5) {
        display: none;
      }
      
      .image-gallery__item:nth-child(4) {
        position: relative;
        
        &::after {
          content: '+' attr(data-remaining);
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.6);
          color: white;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 1.5rem;
          font-weight: 600;
        }
      }
    }
  }
  
  &__item {
    position: relative;
    cursor: pointer;
    overflow: hidden;
    border-radius: var(--border-radius-sm);
    aspect-ratio: 1;
    min-height: 120px;
    
    &:hover {
      .image-gallery__overlay {
        opacity: 1;
      }
    }
  }
  
  &__image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.2s ease;
    
    .image-gallery__item:hover & {
      transform: scale(1.05);
    }
  }
  
  &__overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.2s ease;
  }
  
  &__expand-icon {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    padding: var(--spacing-xs);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--color-text-primary);
    box-shadow: var(--shadow-sm);
  }
}

// Responsive adjustments
@media (max-width: 480px) {
  .image-gallery {
    &__grid {
      &--single {
        max-width: 250px;
      }
      
      &--two,
      &--three,
      &--multiple {
        max-width: 300px;
      }
    }
    
    &__item {
      min-height: 100px;
    }
  }
}
