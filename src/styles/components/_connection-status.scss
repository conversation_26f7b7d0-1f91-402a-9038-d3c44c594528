// Connection Status Component Styles

.connection-status {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  @include text-xs;
  @include transition();
  
  &--success {
    color: var(--color-status-connected);
  }
  
  &--warning {
    color: var(--color-status-connecting);
  }
  
  &--error {
    color: var(--color-status-error);
  }
  
  &--muted {
    color: var(--color-status-disconnected);
  }
  
  &--detailed {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-1);
  }
  
  &--retryable {
    cursor: pointer;
    
    &:hover {
      opacity: 0.8;
    }
  }
}

.connection-status__indicator {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
}

.connection-status__icon--spinning {
  animation: spin 1s linear infinite;
}

.connection-status__text {
  font-weight: var(--font-weight-medium);
  white-space: nowrap;
}

.connection-status__error {
  @include text-xs;
  color: var(--color-error);
  font-style: italic;
  max-width: 200px;
  word-wrap: break-word;
}

.connection-status__retry {
  @include padding(1);
  border: none;
  background: none;
  color: inherit;
  cursor: pointer;
  @include rounded(sm);
  @include transition();
  display: flex;
  align-items: center;
  justify-content: center;
  
  &:hover {
    background: rgba(255, 255, 255, 0.1);
  }
  
  &:focus {
    outline: 1px solid currentColor;
    outline-offset: 2px;
  }
  
  svg {
    @include transition();
  }
  
  &:hover svg {
    transform: rotate(180deg);
  }
}

// Pulse animation for connecting state
.connection-status--warning .connection-status__indicator {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

// Responsive adjustments
@include mobile {
  .connection-status {
    @include text-xs;
  }
  
  .connection-status__text {
    display: none; // Hide text on mobile, show only icon
  }
  
  .connection-status--detailed .connection-status__text {
    display: block;
  }
}

// High contrast mode
@media (prefers-contrast: high) {
  .connection-status {
    font-weight: var(--font-weight-semibold);
  }
  
  .connection-status__retry {
    border: 1px solid currentColor;
  }
}

// Reduced motion
@media (prefers-reduced-motion: reduce) {
  .connection-status__icon--spinning {
    animation: none;
  }
  
  .connection-status--warning .connection-status__indicator {
    animation: none;
  }
  
  .connection-status__retry:hover svg {
    transform: none;
  }
}
