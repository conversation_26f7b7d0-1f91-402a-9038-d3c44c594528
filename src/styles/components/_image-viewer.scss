// Image Viewer Modal Component Styles

.image-viewer {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &__backdrop {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(4px);
  }
  
  &__close {
    position: absolute;
    top: var(--spacing-lg);
    right: var(--spacing-lg);
    z-index: 10;
    background: rgba(255, 255, 255, 0.1);
    border: none;
    border-radius: 50%;
    padding: var(--spacing-sm);
    color: white;
    cursor: pointer;
    transition: background-color 0.2s ease;
    
    &:hover {
      background: rgba(255, 255, 255, 0.2);
    }
    
    &:focus {
      outline: 2px solid rgba(255, 255, 255, 0.5);
      outline-offset: 2px;
    }
  }
  
  &__nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    z-index: 10;
    background: rgba(255, 255, 255, 0.1);
    border: none;
    border-radius: 50%;
    padding: var(--spacing-md);
    color: white;
    cursor: pointer;
    transition: background-color 0.2s ease;
    
    &:hover {
      background: rgba(255, 255, 255, 0.2);
    }
    
    &:focus {
      outline: 2px solid rgba(255, 255, 255, 0.5);
      outline-offset: 2px;
    }
    
    &--prev {
      left: var(--spacing-lg);
    }
    
    &--next {
      right: var(--spacing-lg);
    }
  }
  
  &__container {
    position: relative;
    z-index: 5;
    max-width: 90vw;
    max-height: 90vh;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  &__image {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-lg);
  }
  
  &__counter {
    position: absolute;
    bottom: var(--spacing-lg);
    left: 50%;
    transform: translateX(-50%);
    z-index: 10;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-full);
    font-size: var(--font-size-sm);
    font-weight: 500;
  }
}

// Mobile responsive adjustments
@media (max-width: 768px) {
  .image-viewer {
    &__close {
      top: var(--spacing-md);
      right: var(--spacing-md);
      padding: var(--spacing-xs);
    }
    
    &__nav {
      padding: var(--spacing-sm);
      
      &--prev {
        left: var(--spacing-md);
      }
      
      &--next {
        right: var(--spacing-md);
      }
    }
    
    &__container {
      max-width: 95vw;
      max-height: 85vh;
    }
    
    &__counter {
      bottom: var(--spacing-md);
      font-size: var(--font-size-xs);
    }
  }
}

// Touch device optimizations
@media (hover: none) and (pointer: coarse) {
  .image-viewer {
    &__nav,
    &__close {
      background: rgba(255, 255, 255, 0.2);
      
      &:hover {
        background: rgba(255, 255, 255, 0.2);
      }
    }
  }
}
