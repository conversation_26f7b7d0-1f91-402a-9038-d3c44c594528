// Floating <PERSON><PERSON> Component Styles

.floating-chat-button {
  position: fixed;
  bottom: var(--spacing-6);
  right: var(--spacing-6);
  width: 68px;
  height: 68px;
  border-radius: 50%;
  border: none;
  background: linear-gradient(135deg, var(--color-eneco-purple) 0%, var(--color-secondary) 100%);
  @include shadow(xl);
  cursor: pointer;
  z-index: 9999; // Explicit high z-index to ensure visibility above all content
  @include transition();
  border: 3px solid white;

  // Hover effects
  &:hover {
    transform: scale(1.08);
    @include shadow(2xl);
    background: linear-gradient(135deg, var(--color-secondary) 0%, var(--color-eneco-purple) 100%);
  }

  &:active {
    transform: scale(0.95);
  }

  // Focus styles
  &:focus {
    outline: none;
    box-shadow: 0 0 0 4px rgba(139, 92, 246, 0.3);
  }
  
  // Mobile positioning
  @include mobile {
    bottom: var(--spacing-4);
    right: var(--spacing-4);
    width: 56px;
    height: 56px;
  }
}

.floating-chat-button__avatar {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--color-surface);
}

.floating-chat-button__image {
  width: 80%;
  height: 80%;
  object-fit: cover;
  border-radius: 50%;
}

.floating-chat-button__badge {
  position: absolute;
  top: -4px;
  right: -4px;
  min-width: 20px;
  height: 20px;
  background: var(--color-error);
  color: white;
  border-radius: 10px;
  border: 2px solid var(--color-surface);
  display: flex;
  align-items: center;
  justify-content: center;
  @include text-xs;
  font-weight: var(--font-weight-bold);
  z-index: 1;

  @include mobile {
    min-width: 18px;
    height: 18px;
    top: -3px;
    right: -3px;
  }
}

.floating-chat-button__pulse {
  position: absolute;
  top: -2px;
  right: -2px;
  width: 16px;
  height: 16px;
  background: var(--color-success);
  border-radius: 50%;
  border: 2px solid var(--color-surface);
  opacity: 0;
  @include transition();

  // Pulse animation
  &--active {
    opacity: 1;
    animation: pulse 2s infinite;
  }

  @include mobile {
    width: 14px;
    height: 14px;
  }
}

// Pulse animation keyframes
@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

// Alternative positioning for different layouts
.floating-chat-button--bottom-left {
  right: auto;
  left: var(--spacing-6);
  
  @include mobile {
    left: var(--spacing-4);
  }
}

.floating-chat-button--top-right {
  bottom: auto;
  top: var(--spacing-6);
  
  @include mobile {
    top: var(--spacing-4);
  }
}

.floating-chat-button--top-left {
  bottom: auto;
  right: auto;
  top: var(--spacing-6);
  left: var(--spacing-6);
  
  @include mobile {
    top: var(--spacing-4);
    left: var(--spacing-4);
  }
}
