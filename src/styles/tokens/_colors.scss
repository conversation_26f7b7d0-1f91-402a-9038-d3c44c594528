// Color Tokens - Eneco Brand Colors
// Primary colors - Eneco Purple
$primary-50: #f5f3ff;
$primary-100: #ede9fe;
$primary-200: #ddd6fe;
$primary-300: #c4b5fd;
$primary-400: #a78bfa;
$primary-500: #8b5cf6;
$primary-600: #7c3aed;
$primary-700: #6d28d9;
$primary-800: #5b21b6;
$primary-900: #4c1d95;

// Gray colors
$gray-50: #f9fafb;
$gray-100: #f3f4f6;
$gray-200: #e5e7eb;
$gray-300: #d1d5db;
$gray-400: #9ca3af;
$gray-500: #6b7280;
$gray-600: #4b5563;
$gray-700: #374151;
$gray-800: #1f2937;
$gray-900: #111827;

// Success colors - Eneco Green
$success-50: #f0fdf4;
$success-100: #dcfce7;
$success-200: #bbf7d0;
$success-300: #86efac;
$success-400: #4ade80;
$success-500: #22c55e;
$success-600: #16a34a;
$success-700: #15803d;
$success-800: #166534;
$success-900: #14532d;

// Error colors
$error-50: #fef2f2;
$error-100: #fee2e2;
$error-200: #fecaca;
$error-300: #fca5a5;
$error-400: #f87171;
$error-500: #ef4444;
$error-600: #dc2626;
$error-700: #b91c1c;
$error-800: #991b1b;
$error-900: #7f1d1d;

// Warning colors - Eneco Orange
$warning-50: #fff7ed;
$warning-100: #ffedd5;
$warning-200: #fed7aa;
$warning-300: #fdba74;
$warning-400: #fb923c;
$warning-500: #f97316;
$warning-600: #ea580c;
$warning-700: #c2410c;
$warning-800: #9a3412;
$warning-900: #7c2d12;

// Eneco Brand Colors
$eneco-purple-primary: $primary-700;
$eneco-purple-secondary: $primary-600;
$eneco-purple-light: $primary-100;
$eneco-green: $success-500;
$eneco-orange: $warning-500;
$eneco-white: #ffffff;
$eneco-light-gray: #f8fafc;

// Chat specific colors - Eneco Brand
$chat-user-bg: $primary-600;
$chat-agent-bg: $gray-50;
$chat-user-text: white;
$chat-agent-text: $gray-900;
$chat-header-bg: $primary-700;
$chat-accent: $warning-500;

// Connection status colors
$status-connected: $success-500;
$status-connecting: $warning-500;
$status-disconnected: $gray-400;
$status-error: $error-500;

// Theme color mappings
:root {
  // Light theme (default) - Eneco Brand
  --color-background: #{$eneco-light-gray};
  --color-surface: #{$eneco-white};
  --color-surface-secondary: #{$gray-100};
  --color-border: #{$gray-200};
  --color-border-hover: #{$gray-300};

  --color-text-primary: #{$gray-900};
  --color-text-secondary: #{$gray-600};
  --color-text-muted: #{$gray-400};

  --color-primary: #{$eneco-purple-primary};
  --color-primary-hover: #{$primary-800};
  --color-primary-light: #{$eneco-purple-light};
  --color-secondary: #{$eneco-purple-secondary};

  --color-success: #{$eneco-green};
  --color-error: #{$error-500};
  --color-warning: #{$eneco-orange};

  // Eneco brand colors
  --color-eneco-purple: #{$eneco-purple-primary};
  --color-eneco-green: #{$eneco-green};
  --color-eneco-orange: #{$eneco-orange};
  
  // Chat colors
  --color-chat-user-bg: #{$chat-user-bg};
  --color-chat-agent-bg: #{$chat-agent-bg};
  --color-chat-user-text: #{$chat-user-text};
  --color-chat-agent-text: #{$chat-agent-text};
  
  // Status colors
  --color-status-connected: #{$status-connected};
  --color-status-connecting: #{$status-connecting};
  --color-status-disconnected: #{$status-disconnected};
  --color-status-error: #{$status-error};
}

// Dark theme
[data-theme="dark"] {
  --color-background: #{$gray-900};
  --color-surface: #{$gray-800};
  --color-surface-secondary: #{$gray-700};
  --color-border: #{$gray-600};
  --color-border-hover: #{$gray-500};
  
  --color-text-primary: #{$gray-100};
  --color-text-secondary: #{$gray-300};
  --color-text-muted: #{$gray-500};
  
  --color-primary: #{$primary-400};
  --color-primary-hover: #{$primary-300};
  --color-primary-light: #{$primary-900};
  
  --color-success: #{$success-400};
  --color-error: #{$error-400};
  --color-warning: #{$warning-400};
  
  // Chat colors for dark theme
  --color-chat-user-bg: #{$primary-600};
  --color-chat-agent-bg: #{$gray-700};
  --color-chat-user-text: white;
  --color-chat-agent-text: #{$gray-100};
  
  // Status colors remain the same
  --color-status-connected: #{$status-connected};
  --color-status-connecting: #{$status-connecting};
  --color-status-disconnected: #{$gray-500};
  --color-status-error: #{$status-error};
}
