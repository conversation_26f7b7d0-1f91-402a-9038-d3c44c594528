// Spacing Tokens

// Base spacing unit (4px)
$spacing-unit: 0.25rem;

// Spacing scale
$spacing-0: 0;
$spacing-1: #{$spacing-unit * 1};    // 4px
$spacing-2: #{$spacing-unit * 2};    // 8px
$spacing-3: #{$spacing-unit * 3};    // 12px
$spacing-4: #{$spacing-unit * 4};    // 16px
$spacing-5: #{$spacing-unit * 5};    // 20px
$spacing-6: #{$spacing-unit * 6};    // 24px
$spacing-8: #{$spacing-unit * 8};    // 32px
$spacing-10: #{$spacing-unit * 10};  // 40px
$spacing-12: #{$spacing-unit * 12};  // 48px
$spacing-16: #{$spacing-unit * 16};  // 64px
$spacing-20: #{$spacing-unit * 20};  // 80px
$spacing-24: #{$spacing-unit * 24};  // 96px
$spacing-32: #{$spacing-unit * 32};  // 128px
$spacing-40: #{$spacing-unit * 40};  // 160px
$spacing-48: #{$spacing-unit * 48};  // 192px
$spacing-56: #{$spacing-unit * 56};  // 224px
$spacing-64: #{$spacing-unit * 64};  // 256px

// Border radius
$radius-none: 0;
$radius-sm: 0.125rem;    // 2px
$radius-base: 0.25rem;   // 4px
$radius-md: 0.375rem;    // 6px
$radius-lg: 0.5rem;      // 8px
$radius-xl: 0.75rem;     // 12px
$radius-2xl: 1rem;       // 16px
$radius-3xl: 1.5rem;     // 24px
$radius-full: 9999px;

// Shadows
$shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
$shadow-base: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
$shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
$shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
$shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
$shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
$shadow-inner: inset 0 2px 4px 0 rgb(0 0 0 / 0.05);

// Z-index scale
$z-index-dropdown: 1000;
$z-index-sticky: 1020;
$z-index-fixed: 1030;
$z-index-modal-backdrop: 1040;
$z-index-modal: 1050;
$z-index-popover: 1060;
$z-index-tooltip: 1070;
$z-index-toast: 1080;

// CSS Custom Properties for Spacing
:root {
  --spacing-0: #{$spacing-0};
  --spacing-1: #{$spacing-1};
  --spacing-2: #{$spacing-2};
  --spacing-3: #{$spacing-3};
  --spacing-4: #{$spacing-4};
  --spacing-5: #{$spacing-5};
  --spacing-6: #{$spacing-6};
  --spacing-8: #{$spacing-8};
  --spacing-10: #{$spacing-10};
  --spacing-12: #{$spacing-12};
  --spacing-16: #{$spacing-16};
  --spacing-20: #{$spacing-20};
  --spacing-24: #{$spacing-24};
  --spacing-32: #{$spacing-32};
  --spacing-40: #{$spacing-40};
  --spacing-48: #{$spacing-48};
  --spacing-56: #{$spacing-56};
  --spacing-64: #{$spacing-64};
  
  --radius-none: #{$radius-none};
  --radius-sm: #{$radius-sm};
  --radius-base: #{$radius-base};
  --radius-md: #{$radius-md};
  --radius-lg: #{$radius-lg};
  --radius-xl: #{$radius-xl};
  --radius-2xl: #{$radius-2xl};
  --radius-3xl: #{$radius-3xl};
  --radius-full: #{$radius-full};
  
  --shadow-sm: #{$shadow-sm};
  --shadow-base: #{$shadow-base};
  --shadow-md: #{$shadow-md};
  --shadow-lg: #{$shadow-lg};
  --shadow-xl: #{$shadow-xl};
  --shadow-2xl: #{$shadow-2xl};
  --shadow-inner: #{$shadow-inner};
  
  --z-index-dropdown: #{$z-index-dropdown};
  --z-index-sticky: #{$z-index-sticky};
  --z-index-fixed: #{$z-index-fixed};
  --z-index-modal-backdrop: #{$z-index-modal-backdrop};
  --z-index-modal: #{$z-index-modal};
  --z-index-popover: #{$z-index-popover};
  --z-index-tooltip: #{$z-index-tooltip};
  --z-index-toast: #{$z-index-toast};
}



// Chat specific spacing - Eneco Brand
$chat-message-spacing: $spacing-4;
$chat-bubble-padding: $spacing-4;
$chat-input-padding: $spacing-4;
$floating-chat-size: 440px;
$floating-chat-max-height: 520px;
$floating-chat-expanded-size: 520px;
$floating-chat-expanded-max-height: 680px;

// Eneco specific spacing
$eneco-header-height: 80px;
$eneco-section-padding: $spacing-20;
$eneco-card-padding: $spacing-6;

:root {
  --chat-message-spacing: #{$chat-message-spacing};
  --chat-bubble-padding: #{$chat-bubble-padding};
  --chat-input-padding: #{$chat-input-padding};
  --floating-chat-size: #{$floating-chat-size};
  --floating-chat-max-height: #{$floating-chat-max-height};
  --floating-chat-expanded-size: #{$floating-chat-expanded-size};
  --floating-chat-expanded-max-height: #{$floating-chat-expanded-max-height};

  // Eneco specific spacing
  --eneco-header-height: #{$eneco-header-height};
  --eneco-section-padding: #{$eneco-section-padding};
  --eneco-card-padding: #{$eneco-card-padding};
}
